// Script to check admin user in SQLite database
const { Sequelize } = require('sequelize');
const bcrypt = require('bcrypt');
const path = require('path');

// Initialize Sequelize with SQLite
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: path.join(__dirname, 'data', 'auth.db'),
  logging: false
});

// Define User model (simplified version)
const User = sequelize.define('User', {
  id: {
    type: Sequelize.UUID,
    defaultValue: Sequelize.UUIDV4,
    primaryKey: true,
    allowNull: false,
  },
  username: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true,
  },
  firstName: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  lastName: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  passwordHash: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  isAdmin: {
    type: Sequelize.BOOLEAN,
    defaultValue: false,
  },
  role: {
    type: Sequelize.STRING,
    allowNull: true,
  },
  securityQuestion1Id: {
    type: Sequelize.INTEGER,
    allowNull: false,
  },
  securityAnswer1Hash: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  securityQuestion2Id: {
    type: Sequelize.INTEGER,
    allowNull: false,
  },
  securityAnswer2Hash: {
    type: Sequelize.STRING,
    allowNull: false,
  },
}, {
  timestamps: true,
});

async function checkAdminUser() {
  try {
    console.log('🔍 Checking admin user in SQLite database...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');
    
    // Find admin user
    const adminUser = await User.findOne({
      where: { username: 'admin' }
    });
    
    if (adminUser) {
      console.log('✅ Admin user found:');
      console.log(`   ID: ${adminUser.id}`);
      console.log(`   Username: ${adminUser.username}`);
      console.log(`   Name: ${adminUser.firstName} ${adminUser.lastName}`);
      console.log(`   Role: ${adminUser.role}`);
      console.log(`   isAdmin: ${adminUser.isAdmin}`);
      console.log(`   Password Hash: ${adminUser.passwordHash.substring(0, 20)}...`);
      
      // Test password verification
      console.log('\n🔐 Testing password verification...');
      const testPasswords = ['password123', 'admin123', 'admin', 'Password123'];
      
      for (const testPassword of testPasswords) {
        try {
          const isValid = await bcrypt.compare(testPassword, adminUser.passwordHash);
          console.log(`   Password "${testPassword}": ${isValid ? '✅ VALID' : '❌ Invalid'}`);
          if (isValid) {
            console.log(`   🎉 Found working password: "${testPassword}"`);
            break;
          }
        } catch (error) {
          console.log(`   Password "${testPassword}": ❌ Error - ${error.message}`);
        }
      }
    } else {
      console.log('❌ Admin user not found in database');
      
      // Check if any users exist
      const allUsers = await User.findAll();
      console.log(`\n📊 Total users in database: ${allUsers.length}`);
      
      if (allUsers.length > 0) {
        console.log('\n👥 Existing users:');
        allUsers.forEach(user => {
          console.log(`   - ${user.username} (${user.role}, isAdmin: ${user.isAdmin})`);
        });
      }
    }
    
    // Check all admin users
    const adminUsers = await User.findAll({
      where: {
        [Sequelize.Op.or]: [
          { isAdmin: true },
          { role: 'admin' }
        ]
      }
    });
    
    console.log(`\n👑 Total admin users found: ${adminUsers.length}`);
    adminUsers.forEach(user => {
      console.log(`   - ${user.username} (role: ${user.role}, isAdmin: ${user.isAdmin})`);
    });
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await sequelize.close();
  }
}

async function createAdminUser() {
  try {
    console.log('\n🔧 Creating admin user...');
    
    await sequelize.authenticate();
    
    // Hash password
    const password = 'admin123';
    const passwordHash = await bcrypt.hash(password, 10);
    
    // Hash security answers
    const answer1Hash = await bcrypt.hash('Fluffy', 10);
    const answer2Hash = await bcrypt.hash('Central Elementary', 10);
    
    const adminUser = await User.create({
      username: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      passwordHash: passwordHash,
      isAdmin: true,
      role: 'admin',
      securityQuestion1Id: 1,
      securityAnswer1Hash: answer1Hash,
      securityQuestion2Id: 3,
      securityAnswer2Hash: answer2Hash,
    });
    
    console.log('✅ Admin user created successfully:');
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Username: ${adminUser.username}`);
    console.log(`   Password: ${password}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   isAdmin: ${adminUser.isAdmin}`);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    if (error.name === 'SequelizeUniqueConstraintError') {
      console.log('ℹ️  Admin user already exists');
    }
  } finally {
    await sequelize.close();
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--create')) {
    await createAdminUser();
  } else {
    await checkAdminUser();
  }
}

main().catch(console.error);
