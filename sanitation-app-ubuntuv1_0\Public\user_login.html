
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Warehouse Sanitation Checklists</title>
    <link rel="stylesheet" href="dhl-unified.css">
</head>
<body>
    <div class="page-container">
        <header class="login-header">
            <img src="dhl-logo.svg" alt="DHL Logo" class="logo">
            <h1>Warehouse Sanitation Checklists</h1>
        </header>

        <form id="loginForm" action="/login" method="POST">
            <div class="form-group">
                <label for="username" class="form-label">Employee ID</label>
                <input type="text" id="username" name="username" class="form-control" required>
                <div class="invalid-feedback" id="username-error">Please enter a valid Employee ID</div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
                <div class="invalid-feedback" id="password-error">Please enter your password</div>
            </div>

            <button type="submit" class="btn btn-primary">Log In</button>
        </form>

        <div class="footer" style="text-align: center; margin-top: 25px; font-size: 14px; color: #666;">
            &copy; 2025 DHL Supply Chain | Warehouse Sanitation Checklists
        </div>
    </div>
</body>
</html>

<script>
    document.getElementById('loginForm').addEventListener('submit', function(event) {
        event.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Basic client-side validation
        let isValid = true;
        
        if (!username) {
            document.getElementById('username-error').style.display = 'block';
            isValid = false;
        } else {
            document.getElementById('username-error').style.display = 'none';
        }

        if (!password) {
            document.getElementById('password-error').style.display = 'block';
            isValid = false;
        } else {
            document.getElementById('password-error').style.display = 'none';
        }
        
        if (isValid) {
            // Handle login with server-side validation
            fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '/dashboard';
                } else {
                    alert('Invalid credentials. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again later.');
            });
        }
    });
</script>