<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Link Audit Dashboard - Sanitation App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #d40511;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card.critical {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }
        .stat-card.success {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin: 0;
        }
        .stat-label {
            font-size: 1.1em;
            margin: 5px 0 0 0;
        }
        .role-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .role-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .role-title {
            font-size: 1.3em;
            font-weight: bold;
            margin: 0;
        }
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        .status-critical {
            background: #ff6b6b;
            color: white;
        }
        .status-warning {
            background: #feca57;
            color: #333;
        }
        .status-success {
            background: #48dbfb;
            color: white;
        }
        .role-content {
            padding: 20px;
        }
        .link-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .link-table th,
        .link-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        .link-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .link-status {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-broken {
            background: #ffebee;
            color: #c62828;
        }
        .status-working {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status-unknown {
            background: #fff3e0;
            color: #ef6c00;
        }
        .priority-high {
            color: #d32f2f;
            font-weight: bold;
        }
        .priority-medium {
            color: #f57c00;
            font-weight: bold;
        }
        .priority-low {
            color: #388e3c;
        }
        .action-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .action-section h3 {
            color: #d40511;
            margin-top: 0;
        }
        .action-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #d40511;
            border-radius: 4px;
        }
        .timestamp {
            color: #666;
            font-size: 0.9em;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Link Audit Dashboard</h1>
            <p>Comprehensive Link Health Report for Sanitation Checklist Application</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card critical">
                <div class="stat-number">13</div>
                <div class="stat-label">Broken Links</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number">15</div>
                <div class="stat-label">Working Links</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">143</div>
                <div class="stat-label">Total Static Links</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">99</div>
                <div class="stat-label">Total Routes</div>
            </div>
        </div>

        <div class="role-section">
            <div class="role-header">
                <h3 class="role-title">👑 Admin Role</h3>
                <span class="status-badge status-critical">CRITICAL FAILURE</span>
            </div>
            <div class="role-content">
                <p><strong>Issue:</strong> Complete authentication failure preventing access to admin dashboard</p>
                <table class="link-table">
                    <thead>
                        <tr>
                            <th>Link</th>
                            <th>Target</th>
                            <th>Status</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Admin Dashboard</td>
                            <td>/admin</td>
                            <td><span class="link-status status-broken">BROKEN</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                        <tr>
                            <td>PostgreSQL Dashboard</td>
                            <td>/admin/postgresql-dashboard</td>
                            <td><span class="link-status status-broken">BROKEN</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                        <tr>
                            <td>Create User</td>
                            <td>/admin/users/new</td>
                            <td><span class="link-status status-broken">BROKEN</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                        <tr>
                            <td>Automation Rules</td>
                            <td>/admin/automation-rules</td>
                            <td><span class="link-status status-broken">BROKEN</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="role-section">
            <div class="role-header">
                <h3 class="role-title">👥 Manager Role</h3>
                <span class="status-badge status-warning">PARTIAL SUCCESS</span>
            </div>
            <div class="role-content">
                <p><strong>Status:</strong> 15/20 links working (75% success rate)</p>
                <table class="link-table">
                    <thead>
                        <tr>
                            <th>Link</th>
                            <th>Target</th>
                            <th>Status</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Manager Dashboard</td>
                            <td>/manager</td>
                            <td><span class="link-status status-working">WORKING</span></td>
                            <td><span class="priority-low">LOW</span></td>
                        </tr>
                        <tr>
                            <td>Team Management</td>
                            <td>/manager/teams</td>
                            <td><span class="link-status status-working">WORKING</span></td>
                            <td><span class="priority-low">LOW</span></td>
                        </tr>
                        <tr>
                            <td>Change Password</td>
                            <td>/forgot-password</td>
                            <td><span class="link-status status-broken">BROKEN</span></td>
                            <td><span class="priority-medium">MEDIUM</span></td>
                        </tr>
                        <tr>
                            <td>Compliance Metrics</td>
                            <td>/compliance/metrics</td>
                            <td><span class="link-status status-broken">RATE LIMITED</span></td>
                            <td><span class="priority-medium">MEDIUM</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="role-section">
            <div class="role-header">
                <h3 class="role-title">👤 User Role</h3>
                <span class="status-badge status-critical">AUTHENTICATION FAILURE</span>
            </div>
            <div class="role-content">
                <p><strong>Issue:</strong> User authentication not working, preventing access to user dashboard</p>
                <table class="link-table">
                    <thead>
                        <tr>
                            <th>Link</th>
                            <th>Target</th>
                            <th>Status</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Start New Checklist</td>
                            <td>/app/index.html</td>
                            <td><span class="link-status status-unknown">UNKNOWN</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                        <tr>
                            <td>User Dashboard</td>
                            <td>/checklists/</td>
                            <td><span class="link-status status-unknown">UNKNOWN</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="role-section">
            <div class="role-header">
                <h3 class="role-title">🛡️ Compliance Role</h3>
                <span class="status-badge status-critical">RATE LIMITED</span>
            </div>
            <div class="role-content">
                <p><strong>Issue:</strong> All compliance endpoints returning HTTP 429 (Too Many Requests)</p>
                <table class="link-table">
                    <thead>
                        <tr>
                            <th>Link</th>
                            <th>Target</th>
                            <th>Status</th>
                            <th>Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Compliance Dashboard</td>
                            <td>/compliance</td>
                            <td><span class="link-status status-broken">RATE LIMITED</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                        <tr>
                            <td>Audit Trail</td>
                            <td>/compliance/audit</td>
                            <td><span class="link-status status-broken">RATE LIMITED</span></td>
                            <td><span class="priority-high">CRITICAL</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="action-section">
            <h3>🚨 Immediate Action Required</h3>
            <div class="action-item">
                <strong>1. Fix Admin Authentication (2 hours)</strong><br>
                Verify admin user exists in database and fix authentication middleware
            </div>
            <div class="action-item">
                <strong>2. Resolve Rate Limiting (1 hour)</strong><br>
                Implement role-based rate limiting for compliance endpoints
            </div>
            <div class="action-item">
                <strong>3. Fix User Authentication (2 hours)</strong><br>
                Debug and fix user role authentication system
            </div>
            <div class="action-item">
                <strong>4. Implement Password Reset (3 hours)</strong><br>
                Configure email service and implement working password reset flow
            </div>
        </div>

        <div class="timestamp">
            Report generated: 2025-06-19 18:10:00 UTC<br>
            Next audit scheduled: 2025-06-20 18:10:00 UTC
        </div>
    </div>
</body>
</html>
