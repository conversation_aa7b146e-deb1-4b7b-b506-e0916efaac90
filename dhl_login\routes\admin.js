const express = require('express');
const router = express.Router();

// Authentication middleware for web pages
const ensureWebAuthenticated = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  req.session.returnTo = req.originalUrl;
  req.flash('error', 'Please log in to view that resource.');
  res.redirect('/login-page');
};

// Admin role middleware
const ensureAdmin = (req, res, next) => {
  if (!req.isAuthenticated || !req.isAuthenticated()) {
    req.flash('error', 'Please log in to access this page');
    return res.redirect('/login-page');
  }

  if (!req.user) {
    req.flash('error', 'Authentication error. Please log in again.');
    return res.redirect('/login-page');
  }

  if (req.user.isAdmin === true || req.user.role === 'admin') {
    return next();
  }

  req.flash('error', 'You do not have permission to access this page');
  return res.redirect('/dashboard');
};

// Admin dashboard
router.get('/', ensureWebAuthenticated, ensureAdmin, (req, res) => {
  res.render('admin/dashboard', {
    title: 'Admin Dashboard',
    user: req.user
  });
});

// PostgreSQL Dashboard - simplified route with minimal dependencies
router.get('/postgresql-dashboard', ensureWebAuthenticated, ensureAdmin, (req, res) => {
  console.log('[PostgreSQL Dashboard] Route accessed');
  
  try {
    res.render('admin/postgresql-dashboard', {
      title: 'PostgreSQL Dashboard',
      user: req.user || { username: 'Unknown' },
      backendApiUrl: process.env.BACKEND_API_URL || 'http://localhost:3001'
    });
  } catch (error) {
    console.error('[PostgreSQL Dashboard] Error:', error);
    res.status(500).send('Error loading PostgreSQL dashboard: ' + error.message);
  }
});

// Other admin routes...

module.exports = router;
