Role,Dashboard,Link Text,Target URL,Current Status,HTTP Code,Error Description,Root Cause,Specific Fix Required,File to Modify,Code Change Required,Effort Hours,Priority,Assigned To,Dependencies,Test Steps
admin,/checklists/,Admin Dashboard,/admin,Broken,Auth Failure,Cannot authenticate admin user,Admin user not seeded or password incorrect,Verify admin user exists with correct password hash,dhl_login/models/user.js or seeding script,Check user seeding and password hashing,2,Critical,Backend Dev,Database access,Login with admin credentials
admin,/checklists/,PostgreSQL Dashboard,/admin/postgresql-dashboard,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access admin dashboard first
admin,/checklists/,Create User,/admin/users/new,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access admin dashboard first
admin,/checklists/,Automation Rules,/admin/automation-rules,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access admin dashboard first
admin,/checklists/,System Reports,/admin/reports,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access admin dashboard first
admin,/checklists/,System Logs,/admin/logs,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access admin dashboard first
manager,/checklists/,Change Password,/forgot-password,Broken,200,Error content returned instead of form,Password reset logic not implemented,Implement complete password reset flow,dhl_login/routes/auth.js,Add password reset routes and email config,4,Medium,Backend Dev,Email service config,Submit password reset form
manager,/checklists/,Non-Compliance Reports,/compliance/non-compliance,Broken,429,Rate limiting error,Aggressive rate limiting for compliance endpoints,Implement role-based rate limiting,dhl_login/app.js and backend/server.js,Adjust rate limits for compliance role,2,Medium,DevOps,Rate limit config,Access compliance dashboard
user,/checklists/,Start New Checklist,/app/index.html,Unknown,Auth Failure,User authentication failing,User role login not working,Fix user authentication system,User seeding script,Verify user credentials and auth flow,2,Critical,Backend Dev,Database access,Login with user credentials
user,/checklists/,Change Password,/forgot-password,Broken,200,Error content returned instead of form,Password reset logic not implemented,Implement complete password reset flow,dhl_login/routes/auth.js,Add password reset routes and email config,4,Medium,Backend Dev,Email service config,Submit password reset form
user,/checklists/,Logout,/logout-page,Unknown,Auth Failure,User authentication failing,User role login not working,Fix user authentication system,User seeding script,Verify user credentials and auth flow,2,Critical,Backend Dev,Database access,Login with user credentials
compliance,/compliance,Compliance Dashboard,/compliance,Broken,429,Rate limiting error,Aggressive rate limiting,Implement role-based rate limiting,dhl_login/app.js and backend/server.js,Adjust rate limits for compliance role,2,Critical,DevOps,Rate limit config,Access compliance dashboard
compliance,/compliance,Compliance Metrics,/compliance/metrics,Broken,429,Rate limiting error,Aggressive rate limiting,Implement role-based rate limiting,dhl_login/app.js and backend/server.js,Adjust rate limits for compliance role,2,Critical,DevOps,Rate limit config,Access compliance metrics
compliance,/compliance,Audit Trail,/compliance/audit,Broken,429,Rate limiting error,Aggressive rate limiting,Implement role-based rate limiting,dhl_login/app.js and backend/server.js,Adjust rate limits for compliance role,2,Critical,DevOps,Rate limit config,Access audit trail
compliance,/compliance,Non-Compliance Reports,/compliance/non-compliance,Broken,429,Rate limiting error,Aggressive rate limiting,Implement role-based rate limiting,dhl_login/app.js and backend/server.js,Adjust rate limits for compliance role,2,Critical,DevOps,Rate limit config,Access non-compliance reports
compliance,/compliance,Validation Trends,/compliance/validation-trends,Broken,429,Rate limiting error,Aggressive rate limiting,Implement role-based rate limiting,dhl_login/app.js and backend/server.js,Adjust rate limits for compliance role,2,Critical,DevOps,Rate limit config,Access validation trends
all,all,Password Reset Form,/forgot-password,Broken,200,Error content instead of form,Email service not configured,Configure email service and implement reset logic,dhl_login/routes/auth.js,Add nodemailer config and reset routes,4,Medium,Backend Dev,Email credentials,Submit forgot password form
all,all,Email Configuration,Email Service,Not Working,Config Error,Email service not configured,Missing EMAIL_USER and EMAIL_PASS env vars,Configure email service for password reset,Environment variables,Add EMAIL_USER and EMAIL_PASS to .env,1,Medium,DevOps,Email account,Send test email
admin,/admin/postgresql,PostgreSQL Data Access,/admin/postgresql,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access PostgreSQL dashboard
admin,/admin/postgresql,View Submissions,/admin/postgresql/submissions,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access submissions list
admin,/admin/automation-rules,Create Rule,/admin/automation-rules/new,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access automation rules
admin,/admin/automation-rules,Edit Rule,/admin/automation-rules/:id/edit,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access automation rules
admin,/admin/users,View All Users,/admin/users,Broken,Auth Failure,Admin authentication required,Admin role authentication failing,Fix admin authentication middleware,dhl_login/middleware/authMiddleware.js,Debug ensureAdmin function,1,Critical,Backend Dev,Admin auth fix,Access user management
manager,/manager,Team Management,/manager/teams,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Access team management page
manager,/manager,Performance Analytics,/manager/performance,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Access performance analytics
manager,/manager,Manual Assignments,/manager/assignments,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Access manual assignments
all,/app,Static Checklists,/app/*.html,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Access checklist files
all,/css,Stylesheets,/css/dhl-styles.css,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Load CSS files
all,/,DHL Logo,/dhl-logo.svg,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Load logo image
all,/health,Health Check,/health,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Check health endpoint
all,/health,Detailed Health,/health/detailed,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Check detailed health
all,/health,Database Health,/health/database,Working,200,N/A,N/A,N/A,N/A,N/A,0,Low,N/A,N/A,Check database health
