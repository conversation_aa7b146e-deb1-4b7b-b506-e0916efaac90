<style>
  .submission-detail {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }

  .page-header {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .page-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 300;
  }

  .page-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .detail-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .detail-card h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #2e7d32;
    padding-bottom: 0.5rem;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .detail-row:last-child {
    border-bottom: none;
  }

  .detail-label {
    font-weight: 600;
    color: #555;
  }

  .detail-value {
    color: #333;
    text-align: right;
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-pending {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-pendingsupervisorvalidation {
    background-color: #d1ecf1;
    color: #0c5460;
  }

  .status-supervisorvalidated {
    background-color: #d4edda;
    color: #155724;
  }

  .status-completed {
    background-color: #d4edda;
    color: #155724;
  }

  .full-width {
    grid-column: 1 / -1;
  }

  .headings-table,
  .validations-table,
  .audit-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
  }

  .headings-table th,
  .headings-table td,
  .validations-table th,
  .validations-table td,
  .audit-table th,
  .audit-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
  }

  .headings-table th,
  .validations-table th,
  .audit-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #333;
  }

  .headings-table tr:hover,
  .validations-table tr:hover,
  .audit-table tr:hover {
    background-color: #f9f9f9;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .btn-primary {
    background-color: #2e7d32;
    color: white;
  }

  .btn-primary:hover {
    background-color: #1b5e20;
    color: white;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #545b62;
    color: white;
  }

  .no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
  }

  .filename-cell {
    font-family: monospace;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
  }

  .task-summary {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .task-count {
    background-color: #e3f2fd;
    color: #1565c0;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  @media (max-width: 768px) {
    .submission-detail {
      padding: 1rem;
    }

    .page-header {
      padding: 1.5rem;
    }

    .page-header h1 {
      font-size: 2rem;
    }

    .detail-grid {
      grid-template-columns: 1fr;
    }

    .detail-row {
      flex-direction: column;
      gap: 0.5rem;
    }

    .detail-value {
      text-align: left;
    }

    .action-buttons {
      flex-direction: column;
    }
  }
</style>

<div class="submission-detail">
  <div class="page-header">
    <h1>📋 Submission #<%= submission.submission_id %></h1>
    <p><%= submission.checklist_title %></p>
  </div>

  <div class="action-buttons">
    <a href="/admin/postgresql/submissions" class="btn btn-secondary">← Back to Submissions</a>
    <a href="/admin/postgresql" class="btn btn-secondary">← PostgreSQL Dashboard</a>
  </div>

  <div class="detail-grid">
    <div class="detail-card">
      <h2>📄 Submission Details</h2>
      <div class="detail-row">
        <span class="detail-label">Submission ID:</span>
        <span class="detail-value"><%= submission.submission_id %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Checklist Title:</span>
        <span class="detail-value"><%= submission.checklist_title %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Original Filename:</span>
        <span class="detail-value">
          <% if (submission.original_checklist_filename) { %>
            <span class="filename-cell"><%= submission.original_checklist_filename %></span>
          <% } else { %>
            <em>N/A</em>
          <% } %>
        </span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Submitted By:</span>
        <span class="detail-value"><%= submission.submitted_by_username || 'Unknown' %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">User ID:</span>
        <span class="detail-value"><%= submission.submitted_by_user_id || 'N/A' %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Submission Date:</span>
        <span class="detail-value"><%= new Date(submission.submission_timestamp).toLocaleString() %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Status:</span>
        <span class="detail-value">
          <span class="status-badge status-<%= submission.status.toLowerCase().replace(/\s+/g, '') %>">
            <%= submission.status %>
          </span>
        </span>
      </div>
      <div class="detail-row">
        <span class="detail-label">JSON File:</span>
        <span class="detail-value">
          <% if (submission.json_file_path) { %>
            <span class="filename-cell"><%= submission.json_file_path.split('/').pop() %></span>
          <% } else { %>
            <em>N/A</em>
          <% } %>
        </span>
      </div>
    </div>

    <div class="detail-card">
      <h2>📊 Summary Statistics</h2>
      <div class="detail-row">
        <span class="detail-label">Total Headings:</span>
        <span class="detail-value"><%= headings.length %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Total Tasks:</span>
        <span class="detail-value">
          <%= headings.reduce((sum, h) => sum + parseInt(h.task_count), 0) %>
        </span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Checked Tasks:</span>
        <span class="detail-value">
          <%= headings.reduce((sum, h) => sum + parseInt(h.checked_count), 0) %>
        </span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Validations:</span>
        <span class="detail-value"><%= validations.length %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Audit Entries:</span>
        <span class="detail-value"><%= auditTrail.length %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Created:</span>
        <span class="detail-value"><%= new Date(submission.created_at).toLocaleString() %></span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Last Updated:</span>
        <span class="detail-value"><%= new Date(submission.updated_at).toLocaleString() %></span>
      </div>
    </div>

    <div class="detail-card full-width">
      <h2>📝 Checklist Headings & Tasks</h2>
      <% if (headings && headings.length > 0) { %>
        <table class="headings-table">
          <thead>
            <tr>
              <th>Heading ID</th>
              <th>Heading Text</th>
              <th>Display Order</th>
              <th>Task Count</th>
              <th>Checked Tasks</th>
            </tr>
          </thead>
          <tbody>
            <% headings.forEach(heading => { %>
              <tr>
                <td><%= heading.heading_id %></td>
                <td><%= heading.heading_text %></td>
                <td><%= heading.display_order || 'N/A' %></td>
                <td>
                  <span class="task-count"><%= heading.task_count %> tasks</span>
                </td>
                <td>
                  <span class="task-count"><%= heading.checked_count %> checked</span>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      <% } else { %>
        <div class="no-data">No headings found for this submission.</div>
      <% } %>
    </div>

    <div class="detail-card full-width">
      <h2>✅ Supervisor Validations</h2>
      <% if (validations && validations.length > 0) { %>
        <table class="validations-table">
          <thead>
            <tr>
              <th>Validation ID</th>
              <th>Validated By</th>
              <th>Validation Date</th>
              <th>Overall Status</th>
              <th>Comments</th>
            </tr>
          </thead>
          <tbody>
            <% validations.forEach(validation => { %>
              <tr>
                <td><%= validation.validation_id %></td>
                <td><%= validation.validated_by_username || 'Unknown' %></td>
                <td><%= new Date(validation.validation_timestamp).toLocaleString() %></td>
                <td>
                  <% if (validation.overall_status) { %>
                    <span class="status-badge status-<%= validation.overall_status.toLowerCase() %>">
                      <%= validation.overall_status %>
                    </span>
                  <% } else { %>
                    <em>N/A</em>
                  <% } %>
                </td>
                <td><%= validation.comments || 'No comments' %></td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      <% } else { %>
        <div class="no-data">No supervisor validations found for this submission.</div>
      <% } %>
    </div>

    <div class="detail-card full-width">
      <h2>📋 Audit Trail</h2>
      <% if (auditTrail && auditTrail.length > 0) { %>
        <table class="audit-table">
          <thead>
            <tr>
              <th>Audit ID</th>
              <th>Action Type</th>
              <th>User</th>
              <th>Timestamp</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <% auditTrail.forEach(audit => { %>
              <tr>
                <td><%= audit.audit_id %></td>
                <td>
                  <span class="status-badge" style="background-color: #e1f5fe; color: #01579b;">
                    <%= audit.action_type %>
                  </span>
                </td>
                <td><%= audit.username || 'System' %></td>
                <td><%= new Date(audit.action_timestamp).toLocaleString() %></td>
                <td><%= audit.description || 'No description' %></td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      <% } else { %>
        <div class="no-data">No audit trail entries found for this submission.</div>
      <% } %>
    </div>
  </div>
</div>
