<%- include('../partials/header', { title: title }) %>

<div class="page-container">
  <header class="page-header">
    <h1><i class="fas fa-user-edit"></i> Edit User</h1>
    <p class="page-description">Modify user information and permissions</p>
  </header>

  <!-- Flash Messages -->
  <% if (locals.errorMessages && errorMessages.length > 0) { %>
    <div class="alert alert-danger">
      <% errorMessages.forEach(function(message) { %>
        <p><%= message %></p>
      <% }); %>
    </div>
  <% } %>

  <% if (locals.successMessages && successMessages.length > 0) { %>
    <div class="alert alert-success">
      <% successMessages.forEach(function(message) { %>
        <p><%= message %></p>
      <% }); %>
    </div>
  <% } %>

  <div class="form-container">
    <form action="/admin/users/edit/<%= userToEdit.id %>" method="POST" novalidate>
      <input type="hidden" name="_csrf" value="<%- _csrf %>">

      <div class="form-group">
        <label for="username" class="form-label">Username</label>
        <input type="text" class="form-control <%= (locals.validationErrors && validationErrors.username) ? 'is-invalid' : '' %>"
               id="username" name="username"
               value="<%= locals.formData && formData.username ? formData.username : userToEdit.username %>"
               required>
        <% if (locals.validationErrors && validationErrors.username) { %>
          <div class="invalid-feedback">
            <%- validationErrors.username %>
          </div>
        <% } %>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="firstName" class="form-label">First Name</label>
          <input type="text" class="form-control <%= (locals.validationErrors && validationErrors.firstName) ? 'is-invalid' : '' %>"
                 id="firstName" name="firstName"
                 value="<%= locals.formData && formData.firstName ? formData.firstName : userToEdit.firstName %>"
                 required>
          <% if (locals.validationErrors && validationErrors.firstName) { %>
            <div class="invalid-feedback">
              <%- validationErrors.firstName %>
            </div>
          <% } %>
        </div>

        <div class="form-group">
          <label for="lastName" class="form-label">Last Name</label>
          <input type="text" class="form-control <%= (locals.validationErrors && validationErrors.lastName) ? 'is-invalid' : '' %>"
                 id="lastName" name="lastName"
                 value="<%= locals.formData && formData.lastName ? formData.lastName : userToEdit.lastName %>"
                 required>
          <% if (locals.validationErrors && validationErrors.lastName) { %>
            <div class="invalid-feedback">
              <%- validationErrors.lastName %>
            </div>
          <% } %>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="role" class="form-label">Role</label>
          <select class="form-control" id="role" name="role" required>
            <option value="user" <%= (locals.formData && formData.role === 'user') || userToEdit.role === 'user' ? 'selected' : '' %>>User</option>
            <option value="manager" <%= (locals.formData && formData.role === 'manager') || userToEdit.role === 'manager' ? 'selected' : '' %>>Manager</option>
            <option value="compliance" <%= (locals.formData && formData.role === 'compliance') || userToEdit.role === 'compliance' ? 'selected' : '' %>>Compliance Officer</option>
            <option value="admin" <%= (locals.formData && formData.role === 'admin') || userToEdit.role === 'admin' ? 'selected' : '' %>>Admin</option>
          </select>
        </div>

        <div class="form-group">
          <label for="department" class="form-label">Department</label>
          <input type="text" class="form-control"
                 id="department" name="department"
                 value="<%= locals.formData && formData.department ? formData.department : (userToEdit.department || '') %>">
        </div>
      </div>

      <div class="form-group">
        <label for="managerId" class="form-label">Manager (Optional)</label>
        <select class="form-control" id="managerId" name="managerId">
          <option value="">No Manager</option>
          <% if (locals.managers && managers.length > 0) { %>
            <% managers.forEach(function(manager) { %>
              <% if (manager.id !== userToEdit.id) { %>
                <option value="<%= manager.id %>"
                        <%= (locals.formData && formData.managerId === manager.id) || userToEdit.managerId === manager.id ? 'selected' : '' %>>
                  <%= manager.firstName %> <%= manager.lastName %> (<%= manager.username %>)
                </option>
              <% } %>
            <% }); %>
          <% } %>
        </select>
        <small class="form-help">Select a manager for this user (managers and admins only)</small>
      </div>

      <div class="form-group">
        <input type="checkbox" id="isAdmin" name="isAdmin" value="true" class="form-check-input" 
               <%= (locals.formData && formData.isAdmin) || userToEdit.isAdmin ? 'checked' : '' %>>
        <label for="isAdmin" class="checkbox-label form-check-label">Admin Privileges</label>
      </div>

      <div class="form-section">
        <h3>Change Password (Optional)</h3>
        <p class="text-muted">Leave blank to keep current password</p>
        
        <div class="form-group">
          <label for="newPassword" class="form-label">New Password</label>
          <input type="password" class="form-control <%= (locals.validationErrors && validationErrors.password) ? 'is-invalid' : '' %>"
                 id="newPassword" name="newPassword">
          <% if (locals.validationErrors && validationErrors.password) { %>
            <div class="invalid-feedback">
              <%- validationErrors.password %>
            </div>
          <% } %>
        </div>

        <div class="form-group">
          <label for="confirmPassword" class="form-label">Confirm New Password</label>
          <input type="password" class="form-control"
                 id="confirmPassword" name="confirmPassword">
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> Update User
        </button>
        <a href="/admin/users" class="btn btn-secondary">
          <i class="fas fa-times"></i> Cancel
        </a>
      </div>
    </form>
  </div>
</div>

<style>
.form-container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.form-section h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-actions {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
}

.text-muted {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>

<%- include('../partials/footer') %>
