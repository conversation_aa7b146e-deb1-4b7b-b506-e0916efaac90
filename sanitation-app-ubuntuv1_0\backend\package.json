{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.20.2", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.15", "open": "^10.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.5"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!node_modules/**", "!coverage/**", "!jest.config.js"], "testMatch": ["**/tests/**/*.test.js", "**/tests/**/*.spec.js"]}}