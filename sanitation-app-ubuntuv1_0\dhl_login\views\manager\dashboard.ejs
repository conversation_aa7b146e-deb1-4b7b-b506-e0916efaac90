

<style>
.manager-dashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-header h1 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.dashboard-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-3px);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.card-title {
  color: #2c3e50;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.card-description {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.card-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-secondary:hover {
  background: #d5dbdb;
  transform: translateY(-2px);
}

.loading {
  color: #7f8c8d;
  font-style: italic;
}

.error {
  color: #e74c3c;
  background: #fdf2f2;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #e74c3c;
}

@media (max-width: 768px) {
  .manager-dashboard {
    padding: 1rem;
  }

  .dashboard-header {
    padding: 1.5rem;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>

<div class="manager-dashboard">
  <div class="dashboard-header">
    <h1>Manager Dashboard</h1>
    <p>Welcome back, <%= user ? user.firstName + ' ' + user.lastName : 'Manager' %>! Manage your teams and monitor performance.</p>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-number" id="totalTeamMembers">--</div>
      <div class="stat-label">Team Members</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="activeAssignments">--</div>
      <div class="stat-label">Active Assignments</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="overdueAssignments">--</div>
      <div class="stat-label">Overdue Items</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="completedThisMonth">--</div>
      <div class="stat-label">Completed This Month</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="avgValidationTime">--</div>
      <div class="stat-label">Avg Validation Time (hrs)</div>
    </div>
  </div>

  <!-- Dashboard Cards -->
  <div class="dashboard-grid">
    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">👥</div>
        <h3 class="card-title">Team Management</h3>
      </div>
      <p class="card-description">
        View and manage your team members, assign roles, and monitor team composition across different departments.
      </p>
      <div class="card-actions">
        <a href="/manager/teams" class="btn btn-primary">
          👥 Manage Teams
        </a>
        <a href="/manager/assignments" class="btn btn-secondary">
          📋 Manual Assignments
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">📊</div>
        <h3 class="card-title">Performance Analytics</h3>
      </div>
      <p class="card-description">
        Monitor team performance metrics, completion rates, and identify areas for improvement with detailed analytics.
      </p>
      <div class="card-actions">
        <a href="/manager/performance" class="btn btn-primary">
          📈 View Analytics
        </a>
        <a href="/admin/postgresql" class="btn btn-secondary">
          🗄️ Raw Data
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">⚡</div>
        <h3 class="card-title">Quick Actions</h3>
      </div>
      <p class="card-description">
        Perform common management tasks quickly, including manual checklist assignments and team updates.
      </p>
      <div class="card-actions">
        <a href="/app" class="btn btn-primary">
          🚀 Launch App
        </a>
        <a href="/dashboard" class="btn btn-secondary">
          📱 User Dashboard
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">📋</div>
        <h3 class="card-title">Recent Activity</h3>
      </div>
      <div id="recentActivity" class="loading">Loading recent team activity...</div>
    </div>
  </div>
</div>

<script>
// Manager dashboard functionality
let backendApiUrl = 'http://localhost:3001'; // Default backend URL

// Load dashboard data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadManagerStats();
    loadRecentActivity();
});

// Get JWT token from localStorage or session
function getJWTToken() {
    return localStorage.getItem('jwtToken') || sessionStorage.getItem('jwtToken');
}

// Check if user has JWT token, if not, try to get one
async function ensureAuthentication() {
    const token = getJWTToken();
    if (!token) {
        try {
            const response = await fetch('/api/auth/issue-jwt-for-session', {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    return true;
                }
            }
        } catch (error) {
            console.error('Failed to get JWT token:', error);
        }
        return false;
    }
    return true;
}

// Load manager statistics
async function loadManagerStats() {
    if (!(await ensureAuthentication())) {
        console.error('Authentication failed');
        return;
    }

    const token = getJWTToken();
    
    try {
        const response = await fetch(`${backendApiUrl}/api/manager/stats`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                document.getElementById('totalTeamMembers').textContent = data.stats.totalTeamMembers;
                document.getElementById('activeAssignments').textContent = data.stats.activeAssignments;
                document.getElementById('overdueAssignments').textContent = data.stats.overdueAssignments;
                document.getElementById('completedThisMonth').textContent = data.stats.completedThisMonth;
                document.getElementById('avgValidationTime').textContent = data.stats.avgValidationTime.toFixed(1);
            }
        } else {
            console.error('Failed to load manager stats:', response.statusText);
        }
    } catch (error) {
        console.error('Error loading manager stats:', error);
    }
}

// Load recent activity
async function loadRecentActivity() {
    if (!(await ensureAuthentication())) {
        console.error('Authentication failed');
        return;
    }

    const token = getJWTToken();
    
    try {
        const response = await fetch(`${backendApiUrl}/api/manager/team-assignments?limit=5`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                const activityDiv = document.getElementById('recentActivity');
                if (data.assignments.length === 0) {
                    activityDiv.innerHTML = '<p>No recent team activity.</p>';
                } else {
                    const activityHtml = data.assignments.map(assignment => `
                        <div style="padding: 0.5rem 0; border-bottom: 1px solid #ecf0f1;">
                            <strong>${assignment.checklist_title}</strong><br>
                            <small>Assigned to: ${assignment.assigned_to_user_id} | Status: ${assignment.status}</small>
                        </div>
                    `).join('');
                    activityDiv.innerHTML = activityHtml;
                }
            }
        } else {
            document.getElementById('recentActivity').innerHTML = '<p class="error">Failed to load recent activity.</p>';
        }
    } catch (error) {
        console.error('Error loading recent activity:', error);
        document.getElementById('recentActivity').innerHTML = '<p class="error">Error loading recent activity.</p>';
    }
}
</script>
