# Sanitation App - Root Environment Configuration
# This file contains shared environment variables for the entire application

# =============================================================================
# SHARED CONFIGURATION
# =============================================================================

# Node environment (development, test, production)
# This affects both services
NODE_ENV=development

# =============================================================================
# SERVICE-SPECIFIC CONFIGURATION
# =============================================================================

# Each service has its own .env file with specific configuration:
# - dhl_login/.env.example - Authentication service configuration
# - backend/.env.example   - API service configuration

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. Copy the service-specific .env.example files:
#    cd dhl_login && cp .env.example .env
#    cd ../backend && cp .env.example .env

# 2. Generate secure secrets:
#    node scripts/generate-secrets.js

# 3. Edit each .env file with your specific configuration

# =============================================================================
# PRODUCTION DEPLOYMENT
# =============================================================================

# For production deployment, ensure:
# - Strong, unique secrets for each environment
# - Proper HTTPS configuration
# - Secure email credentials
# - Appropriate CORS settings
# - Database backups configured
