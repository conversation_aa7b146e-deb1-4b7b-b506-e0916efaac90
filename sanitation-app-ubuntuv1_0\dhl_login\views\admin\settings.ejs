<%- include('../partials/header', { title: title }) %>

<div class="page-container">
  <header class="page-header">
    <h1><i class="fas fa-cog"></i> System Settings</h1>
    <p class="page-description">Configure system-wide settings and preferences</p>
  </header>

  <!-- Flash Messages -->
  <% if (locals.errorMessages && errorMessages.length > 0) { %>
    <div class="alert alert-danger">
      <% errorMessages.forEach(function(message) { %>
        <p><%= message %></p>
      <% }); %>
    </div>
  <% } %>

  <% if (locals.successMessages && successMessages.length > 0) { %>
    <div class="alert alert-success">
      <% successMessages.forEach(function(message) { %>
        <p><%= message %></p>
      <% }); %>
    </div>
  <% } %>

  <!-- Action Buttons -->
  <div class="action-bar">
    <a href="/admin" class="btn btn-secondary">
      <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
    </a>
  </div>

  <!-- Settings Sections -->
  <div class="settings-grid">
    
    <!-- Security Settings -->
    <div class="settings-card">
      <div class="settings-header">
        <h3><i class="fas fa-shield-alt"></i> Security Settings</h3>
      </div>
      <div class="settings-content">
        <div class="setting-item">
          <label class="setting-label">Session Timeout (minutes)</label>
          <input type="number" class="form-control" value="60" min="15" max="480">
          <small class="setting-help">How long users stay logged in without activity</small>
        </div>
        
        <div class="setting-item">
          <label class="setting-label">Password Requirements</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input type="checkbox" checked> Minimum 6 characters
            </label>
            <label class="checkbox-item">
              <input type="checkbox" checked> Require special characters
            </label>
            <label class="checkbox-item">
              <input type="checkbox"> Require numbers
            </label>
          </div>
        </div>

        <div class="setting-item">
          <label class="setting-label">Rate Limiting</label>
          <div class="form-row">
            <div class="form-group">
              <label>Max requests per 15 minutes</label>
              <input type="number" class="form-control" value="500" min="100" max="2000">
            </div>
            <div class="form-group">
              <label>Auth attempts before lockout</label>
              <input type="number" class="form-control" value="5" min="3" max="10">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Email Settings -->
    <div class="settings-card">
      <div class="settings-header">
        <h3><i class="fas fa-envelope"></i> Email Configuration</h3>
      </div>
      <div class="settings-content">
        <div class="setting-item">
          <label class="setting-label">SMTP Server</label>
          <input type="text" class="form-control" placeholder="smtp.example.com">
        </div>
        
        <div class="setting-item">
          <div class="form-row">
            <div class="form-group">
              <label>Port</label>
              <input type="number" class="form-control" value="587">
            </div>
            <div class="form-group">
              <label>Security</label>
              <select class="form-control">
                <option value="tls">TLS</option>
                <option value="ssl">SSL</option>
                <option value="none">None</option>
              </select>
            </div>
          </div>
        </div>

        <div class="setting-item">
          <label class="setting-label">From Email Address</label>
          <input type="email" class="form-control" placeholder="<EMAIL>">
        </div>

        <div class="setting-item">
          <button class="btn btn-outline-primary">
            <i class="fas fa-paper-plane"></i> Test Email Configuration
          </button>
        </div>
      </div>
    </div>

    <!-- System Preferences -->
    <div class="settings-card">
      <div class="settings-header">
        <h3><i class="fas fa-sliders-h"></i> System Preferences</h3>
      </div>
      <div class="settings-content">
        <div class="setting-item">
          <label class="setting-label">Default User Role</label>
          <select class="form-control">
            <option value="user">User</option>
            <option value="manager">Manager</option>
            <option value="compliance">Compliance Officer</option>
          </select>
        </div>

        <div class="setting-item">
          <label class="setting-label">Checklist Retention (days)</label>
          <input type="number" class="form-control" value="365" min="30" max="2555">
          <small class="setting-help">How long to keep completed checklists</small>
        </div>

        <div class="setting-item">
          <label class="setting-label">Auto-backup Frequency</label>
          <select class="form-control">
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="disabled">Disabled</option>
          </select>
        </div>

        <div class="setting-item">
          <label class="checkbox-item">
            <input type="checkbox" checked> Enable audit logging
          </label>
          <label class="checkbox-item">
            <input type="checkbox" checked> Enable email notifications
          </label>
          <label class="checkbox-item">
            <input type="checkbox"> Enable debug mode
          </label>
        </div>
      </div>
    </div>

    <!-- Database Settings -->
    <div class="settings-card">
      <div class="settings-header">
        <h3><i class="fas fa-database"></i> Database Configuration</h3>
      </div>
      <div class="settings-content">
        <div class="setting-item">
          <label class="setting-label">Connection Status</label>
          <div class="status-indicator">
            <span class="status-dot status-success"></span>
            <span>Connected</span>
          </div>
        </div>

        <div class="setting-item">
          <label class="setting-label">Pool Size</label>
          <input type="number" class="form-control" value="10" min="5" max="50">
          <small class="setting-help">Maximum number of database connections</small>
        </div>

        <div class="setting-item">
          <button class="btn btn-outline-primary">
            <i class="fas fa-sync-alt"></i> Test Database Connection
          </button>
          <button class="btn btn-outline-warning">
            <i class="fas fa-broom"></i> Clean Old Sessions
          </button>
        </div>
      </div>
    </div>

  </div>

  <!-- Save Settings -->
  <div class="settings-actions">
    <button class="btn btn-primary btn-lg">
      <i class="fas fa-save"></i> Save All Settings
    </button>
    <button class="btn btn-outline-secondary">
      <i class="fas fa-undo"></i> Reset to Defaults
    </button>
  </div>
</div>

<style>
.action-bar {
  margin-bottom: 2rem;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.settings-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.settings-header {
  background: #f8f9fa;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.settings-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.25rem;
}

.settings-content {
  padding: 1.5rem;
}

.setting-item {
  margin-bottom: 1.5rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.setting-help {
  color: #6c757d;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: normal;
  margin-bottom: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
  margin: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-success {
  background-color: #28a745;
}

.status-warning {
  background-color: #ffc107;
}

.status-error {
  background-color: #dc3545;
}

.settings-actions {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-actions .btn {
  margin: 0 0.5rem;
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .settings-actions .btn {
    display: block;
    width: 100%;
    margin: 0.5rem 0;
  }
}
</style>

<%- include('../partials/footer') %>
