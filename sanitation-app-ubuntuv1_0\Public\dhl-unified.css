/* DHL Unified Stylesheet - Combines dhl-styles.css with checklist-specific styles */

/* Delivery Font Definitions */
@font-face {
  font-family: 'Delivery';
  font-style: normal;
  font-weight: 400;
  src: url('fonts/WOFF2/Delivery_W_Rg.woff2') format('woff2'),
       url('fonts/WOFF/Delivery_W_Rg.woff') format('woff');
  font-display: swap;
}

@font-face {
  font-family: 'Delivery';
  font-style: normal;
  font-weight: 700;
  src: url('fonts/WOFF2/Delivery_W_Bd.woff2') format('woff2'),
       url('fonts/WOFF/Delivery_W_Bd.woff') format('woff');
  font-display: swap;
}

@font-face {
  font-family: 'Delivery';
  font-style: italic;
  font-weight: 400;
  src: url('fonts/WOFF2/Delivery_W_It.woff2') format('woff2'),
       url('fonts/WOFF/Delivery_W_It.woff') format('woff');
  font-display: swap;
}

@font-face {
  font-family: 'Delivery';
  font-style: normal;
  font-weight: 300;
  src: url('fonts/WOFF2/Delivery_W_Lt.woff2') format('woff2'),
       url('fonts/WOFF/Delivery_W_Lt.woff') format('woff');
  font-display: swap;
}

/* Core DHL Styles */
body {
  font-family: 'Delivery', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  margin: 0;
  background: linear-gradient(to bottom, #FFCC00, white);
  color: #333333;
  min-height: 100vh;
  padding: 0;
}

/* Container Styles */
.App {
  background: transparent;
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-container {
  max-width: 450px;
  margin: 4rem auto;
  margin-bottom: 11rem;
  padding: 2.5rem;
  background-color: lightgoldenrodyellow;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.header {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 30px;
}

.page-header, .dashboard-header, .login-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
}

.logo {
  height: 80px;
  max-width: 90%;
}

.page-header .logo, .dashboard-header .logo, .login-header .logo {
  height: 50px;
  margin-right: 1.5rem;
}

.page-header h1, .dashboard-header h1, .login-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #D40511; /* DHL Red for the main heading */
  margin: 0;
}

.login-header {
  justify-content: center;
  flex-direction: column;
}

.login-header .logo {
  margin-right: 0;
  margin-bottom: 1rem;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  box-sizing: border-box;
  width: auto;
}

.btn.btn-inline {
  width: auto;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-primary {
  background-color: #FFCC00; /* DHL Yellow */
  color: #333333;
}

.btn-primary:hover {
  background-color: #D40511; /* DHL Red */
  color: white;
}

/* Legacy button styles for compatibility */
.btn.legacy {
  padding: 10px 20px;
  background-color: #FFCC00;
  border: 1px solid #DAA520;
  color: black;
}

.btn.legacy:hover {
  background-color: #F0B000;
  color: #BA0C2F;
  border-color: #BA0C2F;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #FFCC00;
  box-shadow: 0 0 0 2px rgba(255, 204, 0, 0.2);
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333333;
}

/* Message Styles */
.success-message {
  font-size: 1.1rem;
  color: #28a745;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #e9f7ef;
  border-left: 4px solid #28a745;
  border-radius: 4px;
}

.success-message .emoji {
  font-size: 1.2em;
  margin-left: 0.5em;
}

.error-message, .flash-message {
  color: #D40511;
  background-color: #fdecea;
  border: 1px solid #D40511;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
  list-style: none;
}

.flash-message.success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.flash-message.info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: .25rem;
  font-size: .875em;
  color: #D40511;
}

/* Navbar Styles */
.navbar {
  background: #FFCC00;
  padding: 0.8rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
}

.navbar .logo {
  height: 34px;
}

.navbar .user-info {
  color: #333333;
  font-weight: 500;
}

.navbar .user-info span {
  margin-right: 0.5rem;
}

.navbar .user-info a {
  color: #D40511;
  text-decoration: none;
  font-weight: 500;
}

.navbar .user-info a:hover {
  text-decoration: underline;
}

/* Checklist-specific styles from Public/styles.css */
.task-container h3 {
  margin: 40px auto 20px;
}

.button input {
  padding: 10px 20px;
  background-color: #FFCC00;
  color: #BA0C2F;
  border: #DAA520;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  margin: 10px;
}

.button input:hover {
  background-color: #BA0C2F;
  color: whitesmoke;
}

/* Task Container and Grid */
.task-container {
  width: 100%;
  margin: 20px 0;
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.grid div {
  display: flex;
  align-items: center;
}

label {
  font-size: 1rem;
  letter-spacing: 0.5px;
}

h1, h2 {
  color: black;
  text-align: center;
  margin: 20px 0;
}

h3, h4 {
  color: black;
  text-align: left;
  margin: 20px 0 10px 0;
}

.instructional-text {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 20px;
  padding: 0 10%;
}

.no-wrap-heading {
  white-space: nowrap;
}

.highlight-scan {
  background-color: rgb(149, 255, 0) !important;
  transition: background-color 0.3s ease-out;
}

/* Header and Footer Input Styles */
.header-inputs,
.footer-inputs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  flex-wrap: wrap;
}

.header-inputs label,
.footer-inputs label {
  margin-right: 10px;
  font-weight: bold;
}

.header-inputs input,
.footer-inputs input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  background-color: lightyellow;
}

/* Input Styles */
input[type="text"],
input[type="date"],
input[type="time"],
input[type="number"] {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  background-color: lightyellow;
}

input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.2);
}

/* Action List Styles */
.action-list {
  list-style: none;
  padding: 0;
  margin: 20px 0;
  width: 100%;
}

.action-list li {
  margin: 10px 0;
}

.action-list a {
  display: block;
  padding: 15px 20px;
  background-color: #FFCC00;
  color: #333333;
  text-decoration: none;
  border-radius: 5px;
  text-align: center;
  font-weight: 500;
  transition: background-color 0.3s ease, color 0.3s ease;
  border: 1px solid #DAA520;
}

.action-list a:hover {
  background-color: #D40511;
  color: white;
  border-color: #D40511;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .header-inputs,
  .footer-inputs {
    align-items: center;
    flex-direction: column;
  }

  .header-inputs label,
  .footer-inputs label {
    margin: 5px 0;
  }

  .header-inputs input,
  .footer-inputs input {
    width: 100%;
    margin: 5px 0;
  }

  .page-header, .dashboard-header, .login-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-header .logo, .dashboard-header .logo, .login-header .logo {
    margin-bottom: 1rem;
    height: 60px;
    margin-right: 0;
    align-self: flex-start;
  }

  .login-header .logo {
    align-self: center;
  }

  .page-header h1, .dashboard-header h1, .login-header h1 {
    font-size: 1.8rem;
    align-self: flex-start;
  }

  .login-header h1 {
    align-self: center;
  }

  .App {
    padding: 10px;
  }

  .logo {
    height: 60px;
  }
}

/* Comments Section */
.comments {
  margin-top: 2rem;
}
