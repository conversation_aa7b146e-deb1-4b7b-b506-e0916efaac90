<div class="page-container">
  <header class="page-header">
    <h1>Password Reset</h1>
  </header>

  <div id="step1" class="reset-step">
    <h2>Enter Your Username</h2>
    <p>Please enter your username to begin the password reset process.</p>

    <form id="usernameForm">
      <div class="form-group">
        <label for="username" class="form-label">Username</label>
        <input type="text" id="username" name="username" class="form-control" required>
      </div>
      <button type="submit" class="btn btn-primary">Continue</button>
    </form>

    <div class="actions" style="margin-top: 1rem;">
      <a href="/login-page" class="btn btn-secondary btn-inline">Back to Login</a>
    </div>
  </div>

  <div id="step2" class="reset-step" style="display: none;">
    <h2>Security Questions</h2>
    <p>Please answer your security questions to verify your identity.</p>

    <form id="securityForm">
      <input type="hidden" id="resetUsername" name="username">

      <div class="form-group">
        <label id="question1Label" class="form-label"></label>
        <input type="text" id="answer1" name="answer1" class="form-control" required>
        <input type="hidden" id="questionId1" name="questionId1">
      </div>

      <div class="form-group">
        <label id="question2Label" class="form-label"></label>
        <input type="text" id="answer2" name="answer2" class="form-control" required>
        <input type="hidden" id="questionId2" name="questionId2">
      </div>

      <button type="submit" class="btn btn-primary">Verify Answers</button>
    </form>

    <div class="actions" style="margin-top: 1rem;">
      <button id="backToStep1" class="btn btn-secondary btn-inline">Back</button>
    </div>
  </div>

  <div id="step3" class="reset-step" style="display: none;">
    <h2>Set New Password</h2>
    <p>Please enter your new password.</p>

    <form id="passwordForm">
      <input type="hidden" id="finalUsername" name="username">
      <input type="hidden" id="resetToken" name="resetToken">

      <div class="form-group">
        <label for="newPassword" class="form-label">New Password</label>
        <input type="password" id="newPassword" name="newPassword" class="form-control" required minlength="8">
        <small class="form-text text-muted">Password must be at least 8 characters long.</small>
      </div>

      <div class="form-group">
        <label for="confirmPassword" class="form-label">Confirm New Password</label>
        <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" required minlength="8">
      </div>

      <button type="submit" class="btn btn-primary">Reset Password</button>
    </form>
  </div>

  <div id="successMessage" class="reset-step" style="display: none;">
    <div class="success-message">
      <h2>Password Reset Successful!</h2>
      <p>Your password has been successfully reset. You can now log in with your new password.</p>
      <div class="actions">
        <a href="/login-page" class="btn btn-primary">Go to Login</a>
      </div>
    </div>
  </div>

  <div id="errorMessage" class="error-message" style="display: none;"></div>
  <div id="loadingMessage" class="info" style="display: none;">Processing...</div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const step1 = document.getElementById('step1');
  const step2 = document.getElementById('step2');
  const step3 = document.getElementById('step3');
  const successMessage = document.getElementById('successMessage');
  const errorMessage = document.getElementById('errorMessage');
  const loadingMessage = document.getElementById('loadingMessage');

  function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    loadingMessage.style.display = 'none';
  }

  function hideError() {
    errorMessage.style.display = 'none';
  }

  function showLoading() {
    loadingMessage.style.display = 'block';
    hideError();
  }

  function hideLoading() {
    loadingMessage.style.display = 'none';
  }

  // Step 1: Username submission
  document.getElementById('usernameForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    showLoading();

    const username = document.getElementById('username').value;

    try {
      const response = await fetch('/api/auth/request-password-reset-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': '<%- _csrf %>'
        },
        body: JSON.stringify({ username })
      });
      
      const data = await response.json();
      hideLoading();
      
      if (response.ok) {
        // Populate security questions
        document.getElementById('resetUsername').value = username;
        document.getElementById('question1Label').textContent = data.questions[0].text;
        document.getElementById('questionId1').value = data.questions[0].questionId;
        document.getElementById('question2Label').textContent = data.questions[1].text;
        document.getElementById('questionId2').value = data.questions[1].questionId;
        
        // Show step 2
        step1.style.display = 'none';
        step2.style.display = 'block';
        hideError();
      } else {
        showError(data.message || 'User not found.');
      }
    } catch (error) {
      hideLoading();
      showError('An error occurred. Please try again.');
    }
  });

  // Step 2: Security questions submission
  document.getElementById('securityForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    showLoading();
    
    const username = document.getElementById('resetUsername').value;
    const answers = [
      {
        questionId: parseInt(document.getElementById('questionId1').value),
        answer: document.getElementById('answer1').value
      },
      {
        questionId: parseInt(document.getElementById('questionId2').value),
        answer: document.getElementById('answer2').value
      }
    ];
    
    try {
      const response = await fetch('/api/auth/verify-security-answers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': '<%- _csrf %>'
        },
        body: JSON.stringify({ username, answers })
      });
      
      const data = await response.json();
      hideLoading();
      
      if (response.ok) {
        // Store reset token and show step 3
        document.getElementById('finalUsername').value = username;
        document.getElementById('resetToken').value = data.passwordResetToken;
        
        step2.style.display = 'none';
        step3.style.display = 'block';
        hideError();
      } else {
        showError(data.message || 'Incorrect security answers.');
      }
    } catch (error) {
      hideLoading();
      showError('An error occurred. Please try again.');
    }
  });

  // Step 3: Password reset submission
  document.getElementById('passwordForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (newPassword !== confirmPassword) {
      showError('Passwords do not match.');
      return;
    }
    
    showLoading();
    
    const username = document.getElementById('finalUsername').value;
    const passwordResetToken = document.getElementById('resetToken').value;
    
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, passwordResetToken, newPassword })
      });
      
      const data = await response.json();
      hideLoading();
      
      if (response.ok) {
        step3.style.display = 'none';
        successMessage.style.display = 'block';
        hideError();
      } else {
        showError(data.message || 'Failed to reset password.');
      }
    } catch (error) {
      hideLoading();
      showError('An error occurred. Please try again.');
    }
  });

  // Back button functionality
  document.getElementById('backToStep1').addEventListener('click', function() {
    step2.style.display = 'none';
    step1.style.display = 'block';
    hideError();
    // Clear form data
    document.getElementById('answer1').value = '';
    document.getElementById('answer2').value = '';
  });
});
</script>
