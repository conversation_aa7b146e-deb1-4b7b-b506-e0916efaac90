<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - DHL Sanitation Checklists</title>
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <img src="/images/dhl-logo.png" alt="DHL" height="30" class="me-2">
                Sanitation Checklists
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">Dashboard</a>
                <% if (user && (user.role === 'compliance' || user.isAdmin)) { %>
                    <a class="nav-link" href="/compliance">Compliance</a>
                <% } %>
                <% if (user && user.isAdmin) { %>
                    <a class="nav-link" href="/admin">Admin</a>
                <% } %>
                <a class="nav-link" href="/logout">Logout (<%= user ? user.username : 'Unknown' %>)</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
<style>
/* Compliance Dashboard Specific Styles */
.compliance-header {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 8px;
}

.compliance-title {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0;
  text-align: center;
}

.compliance-subtitle {
  text-align: center;
  margin-top: 0.5rem;
  opacity: 0.9;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
  border-left: 4px solid #e74c3c;
}

.stat-card.success {
  border-left-color: #27ae60;
}

.stat-card.warning {
  border-left-color: #f39c12;
}

.stat-card.info {
  border-left-color: #3498db;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.dashboard-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
  background: #f8f9fa;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.card-icon {
  font-size: 2rem;
}

.card-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.card-description {
  padding: 1.5rem;
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
}

.card-actions {
  padding: 1.5rem;
  background: #f8f9fa;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #e74c3c;
  color: white;
}

.btn-primary:hover {
  background: #c0392b;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  color: white;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

.error {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .compliance-title {
    font-size: 2rem;
  }
}
</style>

<div class="container">
  <!-- Header -->
  <div class="compliance-header">
    <h1 class="compliance-title">🛡️ Compliance Dashboard</h1>
    <p class="compliance-subtitle">Monitor compliance metrics, validation trends, and audit trails</p>
  </div>

  <!-- Statistics Cards -->
  <div class="stats-grid">
    <div class="stat-card success">
      <div class="stat-number" id="overallComplianceScore">--</div>
      <div class="stat-label">Overall Compliance Score</div>
    </div>
    <div class="stat-card info">
      <div class="stat-number" id="validatedSubmissions">--</div>
      <div class="stat-label">Validated Submissions (30d)</div>
    </div>
    <div class="stat-card warning">
      <div class="stat-number" id="nonCompliantTasks">--</div>
      <div class="stat-label">Non-Compliant Tasks</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="avgValidationTime">--</div>
      <div class="stat-label">Avg Validation Time (hrs)</div>
    </div>
    <div class="stat-card info">
      <div class="stat-number" id="auditTrailEntries">--</div>
      <div class="stat-label">Audit Trail Entries (7d)</div>
    </div>
  </div>

  <!-- Dashboard Cards -->
  <div class="dashboard-grid">
    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">📊</div>
        <h3 class="card-title">Compliance Metrics</h3>
      </div>
      <p class="card-description">
        View detailed compliance metrics, validation success rates, and quality trends across all checklist types and time periods.
      </p>
      <div class="card-actions">
        <a href="/compliance/metrics" class="btn btn-primary">
          📈 View Metrics
        </a>
        <a href="/manager/performance" class="btn btn-secondary">
          📊 Team Performance
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">🔍</div>
        <h3 class="card-title">Audit Trail</h3>
      </div>
      <p class="card-description">
        Access comprehensive audit trails with filtering capabilities to track all system activities, user actions, and compliance events.
      </p>
      <div class="card-actions">
        <a href="/compliance/audit" class="btn btn-primary">
          🔍 View Audit Trail
        </a>
        <a href="/admin/postgresql" class="btn btn-secondary">
          🗄️ Raw Data
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">⚠️</div>
        <h3 class="card-title">Non-Compliance Reports</h3>
      </div>
      <p class="card-description">
        Drill down into specific non-compliance issues, failed validations, and identify common failure points requiring attention.
      </p>
      <div class="card-actions">
        <a href="/compliance/non-compliance" class="btn btn-primary">
          ⚠️ View Reports
        </a>
        <a href="/compliance/validation-trends" class="btn btn-secondary">
          📈 Validation Trends
        </a>
      </div>
    </div>

    <div class="dashboard-card">
      <div class="card-header">
        <div class="card-icon">📈</div>
        <h3 class="card-title">Validation Trends</h3>
      </div>
      <p class="card-description">
        Analyze validation trends over time, supervisor performance, and identify patterns in compliance and quality metrics.
      </p>
      <div class="card-actions">
        <a href="/compliance/validation-trends" class="btn btn-primary">
          📈 View Trends
        </a>
        <a href="/compliance/metrics" class="btn btn-secondary">
          📊 Detailed Metrics
        </a>
      </div>
    </div>
  </div>
</div>

<script>
// Compliance Dashboard functionality
let backendApiUrl = 'http://localhost:3001'; // Default backend URL

// Load dashboard data on page load
document.addEventListener('DOMContentLoaded', async function() {
    // Ensure authentication before loading data
    const authenticated = await ensureAuthentication();
    if (authenticated) {
        loadComplianceStats();
    } else {
        console.warn('User not authenticated for API calls');
    }
});

// Function to ensure user is authenticated and get JWT token
async function ensureAuthentication() {
    try {
        const response = await fetch('/api/auth/issue-jwt-for-session', {
            method: 'GET',
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.token) {
                localStorage.setItem('jwtToken', data.token);
                return true;
            }
        }
        return false;
    } catch (error) {
        console.error('Authentication check failed:', error);
        return false;
    }
}

// Load compliance statistics
async function loadComplianceStats() {
    const token = localStorage.getItem('jwtToken');
    if (!token) {
        console.error('No JWT token available');
        return;
    }

    try {
        // Load overall compliance metrics
        const response = await fetch(`${backendApiUrl}/api/compliance/overview`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            updateComplianceStats(data);
        } else {
            console.error('Failed to load compliance stats:', response.status);
            showStatsError();
        }
    } catch (error) {
        console.error('Error loading compliance stats:', error);
        showStatsError();
    }
}

// Update compliance statistics display
function updateComplianceStats(data) {
    document.getElementById('overallComplianceScore').textContent = 
        data.overallComplianceScore ? `${data.overallComplianceScore}%` : '--';
    document.getElementById('validatedSubmissions').textContent = 
        data.validatedSubmissions || '--';
    document.getElementById('nonCompliantTasks').textContent = 
        data.nonCompliantTasks || '--';
    document.getElementById('avgValidationTime').textContent = 
        data.avgValidationTime ? `${data.avgValidationTime}h` : '--';
    document.getElementById('auditTrailEntries').textContent = 
        data.auditTrailEntries || '--';
}

// Show error state for statistics
function showStatsError() {
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(element => {
        element.textContent = 'Error';
        element.style.color = '#e74c3c';
    });
}
</script>

    </div> <!-- End main-content -->

    <!-- Footer -->
    <div class="footer" style="text-align: center; margin-top: 2rem; font-size: 0.8rem; color: #666;">
        &copy; 2025 DHL Supply Chain | Warehouse Sanitation Checklists
    </div>

    <!-- Scripts -->
    <script src="/js/bootstrap.bundle.min.js"></script>
</body>
</html>
