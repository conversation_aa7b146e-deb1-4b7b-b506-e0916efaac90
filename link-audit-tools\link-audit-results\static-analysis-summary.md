# Static Link Analysis Summary

**Generated:** 2025-06-19T18:10:20.423Z

## Overview

- **EJS Templates:** 26
- **Route Files:** 9
- **JavaScript Files:** 2
- **Static Checklists:** 30
- **Total Links:** 143
- **Total Routes:** 99

## EJS Templates

### dhl_login\views\admin\automation-rule-form.ejs
- **Links found:** 4
  - `/admin` (line 9)
  - `/admin/automation-rules` (line 10)
  - `/admin/automation-rules` (line 192)
  - `<%= isEdit ? `/admin/automation-rules/${rule.rule_id}` : ` (line 32)

### dhl_login\views\admin\automation-rules.ejs
- **Links found:** 4
  - `/admin/automation-rules/new` (line 8)
  - `/admin` (line 30)
  - `/admin/automation-rules/<%= rule.rule_id %>/edit` (line 95)
  - `/admin/automation-rules/new` (line 117)

### dhl_login\views\admin\backup.ejs
- **Links found:** 1
  - `/admin` (line 34)

### dhl_login\views\admin\create-user.ejs
- **Links found:** 2
  - `/app/index.html` (line 132)
  - `/admin/users` (line 23)

### dhl_login\views\admin\dashboard.ejs
- **Links found:** 12
  - `/admin/users/new` (line 233)
  - `/admin/users` (line 236)
  - `/admin/reports` (line 251)
  - `/admin/logs` (line 254)
  - `/admin/postgresql` (line 269)
  - `/admin/postgresql/submissions` (line 272)
  - `/admin/automation-rules` (line 287)
  - `/admin/automation-rules/new` (line 290)
  - `/admin/settings` (line 305)
  - `/admin/backup` (line 308)
  - `/app` (line 323)
  - `/dashboard` (line 326)

### dhl_login\views\admin\edit-user.ejs
- **Links found:** 2
  - `/admin/users` (line 140)
  - `/admin/users/edit/<%= userToEdit.id %>` (line 27)

### dhl_login\views\admin\logs.ejs
- **Links found:** 1
  - `/admin` (line 152)

### dhl_login\views\admin\postgresql-dashboard.ejs
- **Links found:** 1
  - `/dashboard` (line 39)

### dhl_login\views\admin\postgresql-submission-detail.ejs
- **Links found:** 2
  - `/admin/postgresql/submissions` (line 241)
  - `/admin/postgresql` (line 242)

### dhl_login\views\admin\postgresql-submissions.ejs
- **Links found:** 5
  - `/admin/postgresql` (line 211)
  - `/admin` (line 212)
  - `/admin/postgresql/submissions/<%= submission.submission_id %>` (line 233)
  - `?page=<%= currentPage - 1 %>` (line 267)
  - `?page=<%= currentPage + 1 %>` (line 275)

### dhl_login\views\admin\reports.ejs
- **Links found:** 7
  - `/admin` (line 218)
  - `/admin/postgresql/submissions` (line 283)
  - `/admin/postgresql` (line 286)
  - `/admin/logs` (line 306)
  - `/admin/users/new` (line 323)
  - `/admin/automation-rules` (line 343)
  - `/admin/automation-rules/new` (line 346)

### dhl_login\views\admin\settings.ejs
- **Links found:** 1
  - `/admin` (line 28)

### dhl_login\views\admin\users-list.ejs
- **Links found:** 5
  - `/admin/users/new` (line 28)
  - `/admin` (line 39)
  - `/admin/users/edit/<%= userItem.id %>` (line 85)
  - `/admin/users/bulk-role-change` (line 190)
  - `/admin/users/bulk-delete` (line 231)

### dhl_login\views\compliance\dashboard.ejs
- **Links found:** 16
  - `/css/bootstrap.min.css` (line 7)
  - `/css/style.css` (line 8)
  - `/favicon.ico` (line 9)
  - `/dashboard` (line 15)
  - `/dashboard` (line 20)
  - `/compliance` (line 22)
  - `/admin` (line 25)
  - `/logout` (line 27)
  - `/compliance/metrics` (line 260)
  - `/manager/performance` (line 263)
  - `/compliance/audit` (line 278)
  - `/admin/postgresql` (line 281)
  - `/compliance/non-compliance` (line 296)
  - `/compliance/validation-trends` (line 299)
  - `/compliance/validation-trends` (line 314)
  - `/compliance/metrics` (line 317)

### dhl_login\views\compliance\metrics.ejs
- **Links found:** 1
  - `/compliance` (line 73)

### dhl_login\views\dashboard.ejs
- **Links found:** 18
  - `/app/index.html` (line 252)
  - `/forgot-password` (line 258)
  - `/compliance` (line 286)
  - `/compliance/metrics` (line 289)
  - `/compliance/audit` (line 292)
  - `/compliance/non-compliance` (line 295)
  - `/manager` (line 310)
  - `/manager/teams` (line 313)
  - `/manager/performance` (line 316)
  - `/manager/assignments` (line 319)
  - `/admin` (line 335)
  - `/admin/postgresql-dashboard` (line 339)
  - `/admin/automation-rules` (line 343)
  - `/admin/create-user` (line 347)
  - `/login-page` (line 377)
  - `/app/index.html` (line 632)
  - `/login-page` (line 377)
  - `/app/index.html` (line 632)

### dhl_login\views\error.ejs
- **Links found:** 4
  - `/dashboard` (line 175)
  - `/login-page` (line 179)
  - `/dashboard` (line 218)
  - `/dashboard` (line 218)

### dhl_login\views\forgot-password.ejs
- **Links found:** 2
  - `/login-page` (line 19)
  - `/login-page` (line 78)

### dhl_login\views\layouts\main.ejs
- **Links found:** 2
  - `/css/dhl-styles.css` (line 9)
  - `/logout-page` (line 17)

### dhl_login\views\layouts\test_layout.ejs
- **Links found:** 0

### dhl_login\views\login.ejs
- **Links found:** 2
  - `/forgot-password` (line 27)
  - `/login-page` (line 7)

### dhl_login\views\manager\assignments.ejs
- **Links found:** 1
  - `/manager` (line 222)

### dhl_login\views\manager\dashboard.ejs
- **Links found:** 6
  - `/manager/teams` (line 236)
  - `/manager/assignments` (line 239)
  - `/manager/performance` (line 254)
  - `/admin/postgresql` (line 257)
  - `/app` (line 272)
  - `/dashboard` (line 275)

### dhl_login\views\manager\performance.ejs
- **Links found:** 1
  - `/manager` (line 240)

### dhl_login\views\manager\teams.ejs
- **Links found:** 1
  - `/manager` (line 158)

### dhl_login\views\test_page.ejs
- **Links found:** 0

## Routes

### dhl_login\routes\admin.js
- **GET** `/` (line 35)
- **GET** `/postgresql-dashboard` (line 43)

### dhl_login\routes\auth.js
- **GET** `/security-questions` (line 50)
- **POST** `/register` (line 56)
- **POST** `/login-api` (line 105)
- **POST** `/request-password-reset-questions` (line 138)
- **POST** `/verify-security-answers` (line 176)
- **POST** `/reset-password` (line 253)
- **GET** `/issue-jwt-for-session` (line 292)
- **POST** `/token` (line 326)
- **GET** `/me` (line 362)
- **GET** `/users/by-role/:roleName` (line 374)
- **GET** `/users/:userId/details` (line 425)
- **GET** `/users` (line 482)

### dhl_login\routes\checklist.js
- **GET** `/` (line 19)

### dhl_login\routes\compliance.js
- **GET** `/` (line 18)
- **GET** `/metrics` (line 28)
- **GET** `/audit` (line 37)
- **GET** `/non-compliance` (line 46)
- **GET** `/validation-trends` (line 55)

### dhl_login\routes\health.js
- **GET** `/health` (line 16)
- **GET** `/health/detailed` (line 34)
- **GET** `/health/database` (line 93)
- **GET** `/health/error-test/:type` (line 126)
- **GET** `/health/auth-test` (line 157)
- **GET** `/ready` (line 223)
- **GET** `/live` (line 258)

### dhl_login\routes\manager.js
- **GET** `/` (line 18)
- **GET** `/teams` (line 27)
- **GET** `/performance` (line 36)
- **GET** `/assignments` (line 45)
- **GET** `/test-auth` (line 54)

### backend\routes\health.js
- **GET** `/health` (line 17)
- **GET** `/health/detailed` (line 35)
- **GET** `/health/error-test/:type` (line 110)
- **GET** `/health/error-stats` (line 150)
- **GET** `/ready` (line 189)
- **GET** `/live` (line 214)

### dhl_login\app.js
- **GET** `/dhl-logo.svg` (line 123)
- **GET** `/app/validate-checklist/:id` (line 148)
- **GET** `/app` (line 169)
- **GET** `/` (line 176)
- **GET** `/login-page` (line 180)
- **POST** `/login-page` (line 191)
- **GET** `/logout-page` (line 242)
- **GET** `/dashboard` (line 253)
- **GET** `/forgot-password` (line 264)
- **GET** `/api/config` (line 276)
- **GET** `/health/database` (line 325)
- **/API** `/api` (line 59)
- **/API** `/api` (line 60)
- **/APP** `/app` (line 158)
- **/APP** `/app` (line 167)
- **/API/AUTH** `/api/auth` (line 290)
- **/ADMIN** `/admin` (line 293)
- **/MANAGER** `/manager` (line 302)
- **/COMPLIANCE** `/compliance` (line 311)
- **/CHECKLISTS** `/checklists` (line 320)
- **/** `/` (line 323)

### backend\server.js
- **POST** `/submit-form` (line 198)
- **GET** `/validate/:id` (line 417)
- **POST** `/validate/:id` (line 464)
- **GET** `/validate-status/:id` (line 609)
- **GET** `/view-checklist/:id` (line 636)
- **GET** `/view-checklist-html/:id` (line 660)
- **GET** `/api/user/assignments` (line 698)
- **GET** `/api/user/submissions` (line 737)
- **GET** `/api/assignments/:assignmentId` (line 782)
- **PATCH** `/api/assignments/:assignmentId/status` (line 855)
- **GET** `/api/teams` (line 911)
- **GET** `/api/teams/:teamId/members` (line 953)
- **GET** `/api/user/teams` (line 991)
- **POST** `/api/teams/:teamId/members` (line 1022)
- **DELETE** `/api/teams/:teamId/members/:userId` (line 1085)
- **GET** `/api/manager/stats` (line 1136)
- **GET** `/api/manager/team-assignments` (line 1274)
- **GET** `/api/manager/team-performance` (line 1372)
- **GET** `/api/manager/audit-trail` (line 1474)
- **GET** `/api/manager/compliance-report` (line 1527)
- **GET** `/api/analytics/submissions` (line 1569)
- **GET** `/api/analytics/team-performance` (line 1628)
- **GET** `/api/analytics/compliance` (line 1668)
- **GET** `/api/analytics/assignments` (line 1705)
- **GET** `/api/manager/available-checklists` (line 1752)
- **POST** `/api/manager/manual-assignment` (line 1791)
- **GET** `/api/manager/manual-assignments` (line 1903)
- **PATCH** `/api/manager/manual-assignment/:assignmentId` (line 1969)
- **GET** `/api/analytics/completion-trends` (line 2032)
- **GET** `/api/analytics/validation-turnaround` (line 2100)
- **GET** `/api/analytics/team-productivity` (line 2137)
- **GET** `/api/user/stats` (line 2202)
- **GET** `/api/compliance/overview` (line 2258)
- **GET** `/api/compliance/metrics` (line 2350)
- **GET** `/api/compliance/audit-trail` (line 2397)
- **GET** `/api/compliance/non-compliance` (line 2468)
- **GET** `/api/admin/scheduled-automation/status` (line 2567)
- **GET** `/api/admin/rpa/status` (line 2613)
- **POST** `/api/admin/rpa/trigger` (line 2656)
- **/** `/` (line 2697)

## Static Checklists

### 10_E_Cell_East_Side_Daily.html
- **Size:** 7KB
- **Links:** 1
  - `dhl-unified.css`

### 11_F_Cell_West_Side_Daily.html
- **Size:** 5KB
- **Links:** 1
  - `dhl-unified.css`

### 12_F_Cell_East_Side_Daily.html
- **Size:** 7KB
- **Links:** 1
  - `dhl-unified.css`

### 13_All_Cells_Weekly.html
- **Size:** 8KB
- **Links:** 1
  - `dhl-unified.css`

### 14_All_Cells_Weekly.html
- **Size:** 7KB
- **Links:** 1
  - `dhl-unified.css`

### 15_A&B_Cells_LL_Quarterly.html
- **Size:** 10KB
- **Links:** 1
  - `dhl-unified.css`

### 16_D_Cell_LL_Quarterly.html
- **Size:** 5KB
- **Links:** 1
  - `dhl-unified.css`

### 17_A_Cell_High_Level_Quarterly.html
- **Size:** 5KB
- **Links:** 1
  - `dhl-unified.css`

### 18_B_Cell_High_Level_Quarterly.html
- **Size:** 5KB
- **Links:** 1
  - `dhl-unified.css`

### 19_C_Cell_High_Level_Quarterly.html
- **Size:** 5KB
- **Links:** 1
  - `dhl-unified.css`

### 1_A_Cell_West_Side_Daily.html
- **Size:** 7KB
- **Links:** 1
  - `dhl-unified.css`

### 1_A_Cell_West_Side_Weekly.html
- **Size:** 7KB
- **Links:** 1
  - `dhl-unified.css`

### 20_D_Cell_High_Level_Quarterly.html
- **Size:** 5KB
- **Links:** 1
  - `dhl-unified.css`

### 21_E_Cell_High_Level_Quarterlyl.html
- **Size:** 5KB
- **Links:** 1
  - `dhl-unified.css`

### 22_F_Cell_High_Level_Quarterlyl.html
- **Size:** 6KB
- **Links:** 1
  - `dhl-unified.css`

### 2_A_Cell_East_Side_Daily.html
- **Size:** 6KB
- **Links:** 1
  - `dhl-unified.css`

### 2_B_Cell_East_Side_Daily.html
- **Size:** 6KB
- **Links:** 1
  - `dhl-unified.css`

### 3_B_Cell_West_Side_Daily.html
- **Size:** 4KB
- **Links:** 1
  - `dhl-unified.css`

### 4_B_Cell_East_Side_Daily.html
- **Size:** 9KB
- **Links:** 1
  - `dhl-unified.css`

### 5_C_Cell_West_Side_Daily.html
- **Size:** 7KB
- **Links:** 1
  - `dhl-unified.css`

### 6_C_Cell_East_Side_Daily.html
- **Size:** 8KB
- **Links:** 1
  - `dhl-unified.css`

### 7_D_Cell_West_Side_Daily.html
- **Size:** 8KB
- **Links:** 1
  - `dhl-unified.css`

### 8_D_Cell_East_Side_Daily.html
- **Size:** 8KB
- **Links:** 1
  - `dhl-unified.css`

### 9_E_Cell_West_Side_Daily.html
- **Size:** 8KB
- **Links:** 1
  - `dhl-unified.css`

### barcode_generator.html
- **Size:** 17KB
- **Links:** 0

### checklist-template.html
- **Size:** 1KB
- **Links:** 1
  - `dhl-unified.css`

### index.html
- **Size:** 2KB
- **Links:** 2
  - `dhl-unified.css`
  - `/login-page`

### test-scan.html
- **Size:** 8KB
- **Links:** 1
  - `dhl-unified.css`

### user_login.html
- **Size:** 3KB
- **Links:** 4
  - `dhl-unified.css`
  - `/dashboard`
  - `/login`
  - `/dashboard`

### validate-checklist.html
- **Size:** 37KB
- **Links:** 4
  - `/dashboard`
  - `/login-page`
  - `/dashboard`
  - `/dashboard`

