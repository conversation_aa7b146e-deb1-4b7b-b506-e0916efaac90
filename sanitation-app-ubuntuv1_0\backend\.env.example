# Backend API Service Environment Configuration
# Copy this file to .env and replace the placeholder values with your actual configuration

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Port for the backend API service to run on
# Default: 3001
PORT=3001

# Node environment (development, test, production)
# Default: development
NODE_ENV=development

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT secret for token signing and verification
# IMPORTANT: Must match the JWT_SECRET in dhl_login/.env
# Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=jwt_secret_here

# CORS allowed origins (comma-separated list)
# Development: http://localhost:3000 (dhl_login service)
# Production: Add your production frontend URLs
# Examples:
#   Single origin: http://localhost:3000
#   Multiple origins: http://localhost:3000,https://yourdomain.com,https://www.yourdomain.com
CORS_ALLOWED_ORIGINS=http://localhost:3000

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Base URL for the frontend service (dhl_login)
# Used for generating checklist validation links in emails
# Development: http://localhost:3000
# Production: https://dot1hundred.com (via nginx proxy)
BASE_URL=http://localhost:3000

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Supervisor email address for receiving checklist notifications
# This email will receive all submitted checklists for validation
# Should match the SUPERVISOR_EMAIL in dhl_login/.env
SUPERVISOR_EMAIL=<EMAIL>

# Gmail SMTP configuration for sending emails
# You need to enable "App Passwords" in your Gmail account for this to work
# See: https://support.google.com/accounts/answer/185833
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password-here

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL)
# =============================================================================
# Used for the new checklist automation, tracking, and BI features.
# User authentication remains with dhl_login service's SQLite DB.

PG_HOST=localhost
PG_PORT=5432
PG_USER=your_pg_user
PG_PASSWORD=your_pg_password
PG_DATABASE=sanitation_checklist_db
# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Enable debug logging (true/false)
# Default: false
# DEBUG=false

# =============================================================================
# PRODUCTION NOTES
# =============================================================================
# For production deployment:
# 1. Set NODE_ENV=production
# 2. Use strong, unique JWT_SECRET that matches dhl_login service
# 3. Configure CORS_ALLOWED_ORIGINS with your actual frontend domains
# 4. Set up proper email credentials (EMAIL_USER and EMAIL_PASS)
# 5. Update BASE_URL to your production frontend URL
# 6. Consider using environment-specific email settings
# 7. Enable HTTPS and ensure all URLs use https:// in production

# =============================================================================
# CORS SECURITY NOTES
# =============================================================================
# The CORS_ALLOWED_ORIGINS setting is critical for security:
# - Never use '*' (wildcard) in production as it allows any origin
# - Always specify exact origins including protocol (http/https) and port
# - For production, use only HTTPS origins
# - Test CORS configuration thoroughly before deployment
# 
# Examples of secure CORS configurations:
# Development: http://localhost:3000
# Production: https://yourdomain.com,https://www.yourdomain.com
# Mixed: http://localhost:3000,https://staging.yourdomain.com,https://yourdomain.com
