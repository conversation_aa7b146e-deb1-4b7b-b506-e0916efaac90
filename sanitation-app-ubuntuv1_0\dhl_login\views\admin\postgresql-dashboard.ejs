<%- include('../partials/header', { title: 'PostgreSQL Dashboard' }) %>

<div class="container mt-4">
  <h1>PostgreSQL Dashboard</h1>
  
  <div class="alert alert-info">
    <p>This is a simple PostgreSQL dashboard. Database statistics will be displayed here.</p>
  </div>
  
  <div class="row">
    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header">
          Database Statistics
        </div>
        <div class="card-body">
          <p>Database Size: <span id="dbSize">Loading...</span></p>
          <p>Total Tables: <span id="totalTables">Loading...</span></p>
          <p>Total Records: <span id="totalRecords">Loading...</span></p>
        </div>
      </div>
    </div>
    
    <div class="col-md-6">
      <div class="card mb-4">
        <div class="card-header">
          Connection Information
        </div>
        <div class="card-body">
          <p>Host: <%= process.env.PG_HOST || 'localhost' %></p>
          <p>Database: <%= process.env.PG_DATABASE || 'sanitation_checklist_db' %></p>
          <p>Status: <span id="connectionStatus">Checking...</span></p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="mt-3">
    <a href="/dashboard" class="btn btn-secondary">Back to Dashboard</a>
  </div>
</div>

<script>
  // Simple placeholder script
  document.getElementById('dbSize').textContent = 'N/A';
  document.getElementById('totalTables').textContent = 'N/A';
  document.getElementById('totalRecords').textContent = 'N/A';
  document.getElementById('connectionStatus').textContent = 'Connected';
</script>

<%- include('../partials/footer') %>
